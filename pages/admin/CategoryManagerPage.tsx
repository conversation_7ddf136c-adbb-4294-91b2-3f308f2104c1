
import React, { useState, useContext } from 'react';
import { BlogContext } from '../../context/SupabaseBlogContext';
import { TrashIcon, PlusIcon } from '../../components/icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const CategoryManagerPage: React.FC = () => {
    const [newCategory, setNewCategory] = useState('');
    const [newTag, setNewTag] = useState('');
    const [isAddingCategory, setIsAddingCategory] = useState(false);
    const [isAddingTag, setIsAddingTag] = useState(false);
    const context = useContext(BlogContext);

    if (!context) {
        return (
            <div className="flex items-center justify-center h-full">
                <div className="text-muted-foreground">Loading...</div>
            </div>
        );
    }

    const { categories, tags, addCategory, deleteCategory, addTag, deleteTag } = context;

    const handleAddCategory = async (e: React.FormEvent) => {
        e.preventDefault();
        if (newCategory.trim()) {
            setIsAddingCategory(true);
            try {
                await addCategory(newCategory.trim());
                setNewCategory('');
            } catch (error) {
                console.error('Failed to add category:', error);
            } finally {
                setIsAddingCategory(false);
            }
        }
    };

    const handleAddTag = async (e: React.FormEvent) => {
        e.preventDefault();
        if (newTag.trim()) {
            setIsAddingTag(true);
            try {
                await addTag(newTag.trim());
                setNewTag('');
            } catch (error) {
                console.error('Failed to add tag:', error);
            } finally {
                setIsAddingTag(false);
            }
        }
    };

    const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
        if (window.confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
            try {
                await deleteCategory(categoryId);
            } catch (error) {
                console.error('Failed to delete category:', error);
            }
        }
    };

    const handleDeleteTag = async (tagId: string, tagName: string) => {
        if (window.confirm(`Are you sure you want to delete the tag "${tagName}"? This action cannot be undone.`)) {
            try {
                await deleteTag(tagId);
            } catch (error) {
                console.error('Failed to delete tag:', error);
            }
        }
    };

    return (
        <div className="h-full flex flex-col space-y-6">
            {/* Header */}
            <div className="flex-shrink-0">
                <h1 className="text-3xl font-bold tracking-tight">Manage Categories & Tags</h1>
                <p className="text-muted-foreground">
                    Organize your content with categories and tags
                </p>
            </div>

            {/* Content Grid */}
            <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 min-h-0">
                {/* Categories Section */}
                <Card className="h-full">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            Categories
                            <Badge variant="secondary">{categories.length}</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {/* Add Category Form */}
                        <form onSubmit={handleAddCategory} className="flex gap-2">
                            <Input
                                type="text"
                                value={newCategory}
                                onChange={(e) => setNewCategory(e.target.value)}
                                placeholder="New category name"
                                className="flex-1"
                                disabled={isAddingCategory}
                            />
                            <Button type="submit" disabled={isAddingCategory || !newCategory.trim()}>
                                {isAddingCategory ? (
                                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                                ) : (
                                    <PlusIcon className="w-4 h-4" />
                                )}
                            </Button>
                        </form>

                        <Separator />

                        {/* Categories List */}
                        <ScrollArea className="h-[300px]">
                            <div className="space-y-2">
                                {categories.length === 0 ? (
                                    <div className="text-center text-muted-foreground py-8">
                                        No categories yet. Add your first category above.
                                    </div>
                                ) : (
                                    categories.map((category, index) => (
                                        <div key={category.id}>
                                            <div className="flex items-center justify-between p-3 rounded-lg border">
                                                <span className="font-medium">{category.name}</span>
                                                <TooltipProvider>
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleDeleteCategory(category.id, category.name)}
                                                                className="text-destructive hover:text-destructive h-8 w-8"
                                                            >
                                                                <TrashIcon className="w-4 h-4" />
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>Delete category</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            </div>
                                            {index < categories.length - 1 && <Separator className="my-2" />}
                                        </div>
                                    ))
                                )}
                            </div>
                        </ScrollArea>
                    </CardContent>
                </Card>
                {/* Tags Section */}
                <Card className="h-full">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            Tags
                            <Badge variant="secondary">{tags.length}</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {/* Add Tag Form */}
                        <form onSubmit={handleAddTag} className="flex gap-2">
                            <Input
                                type="text"
                                value={newTag}
                                onChange={(e) => setNewTag(e.target.value)}
                                placeholder="New tag name"
                                className="flex-1"
                                disabled={isAddingTag}
                            />
                            <Button type="submit" disabled={isAddingTag || !newTag.trim()}>
                                {isAddingTag ? (
                                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                                ) : (
                                    <PlusIcon className="w-4 h-4" />
                                )}
                            </Button>
                        </form>

                        <Separator />

                        {/* Tags List */}
                        <ScrollArea className="h-[300px]">
                            <div className="space-y-3">
                                {tags.length === 0 ? (
                                    <div className="text-center text-muted-foreground py-8">
                                        No tags yet. Add your first tag above.
                                    </div>
                                ) : (
                                    <div className="flex flex-wrap gap-2">
                                        {tags.map(tag => (
                                            <Badge
                                                key={tag.id}
                                                variant="secondary"
                                                className="flex items-center gap-2 px-3 py-1 text-sm"
                                            >
                                                <span>{tag.name}</span>
                                                <TooltipProvider>
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <button
                                                                onClick={() => handleDeleteTag(tag.id, tag.name)}
                                                                className="text-muted-foreground hover:text-destructive transition-colors"
                                                            >
                                                                <TrashIcon className="w-3 h-3" />
                                                            </button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>Delete tag</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            </Badge>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default CategoryManagerPage;
