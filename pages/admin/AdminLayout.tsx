
import React, { useContext } from 'react';
import { NavLink, Outlet, useNavigate } from 'react-router-dom';
import { BlogContext } from '../../context/SupabaseBlogContext';
import { useSidebar } from '../../context/SidebarContext';
import {
  ChartBarIcon,
  DocumentTextIcon,
  TagIcon,
  ArrowLeftOnRectangleIcon,
  MenuIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '../../components/icons.tsx';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

const AdminLayout: React.FC = () => {
    const context = useContext(BlogContext);
    const navigate = useNavigate();
    const { isCollapsed, isMobileOpen, toggleCollapsed, setMobileOpen, closeMobile } = useSidebar();

    const handleLogout = () => {
        context?.logout();
        navigate('/login');
    };

    const navItems = [
        {
            to: "/admin",
            icon: ChartBarIcon,
            label: "Dashboard",
            end: true
        },
        {
            to: "/admin/posts",
            icon: DocumentTextIcon,
            label: "Posts"
        },
        {
            to: "/admin/categories",
            icon: TagIcon,
            label: "Categories & Tags"
        }
    ];

    const NavItem = ({ item, mobile = false }: { item: typeof navItems[0], mobile?: boolean }) => (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <NavLink
                        to={item.to}
                        end={item.end}
                        onClick={mobile ? closeMobile : undefined}
                        className={({ isActive }) => cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent",
                            isActive
                                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                                : "text-muted-foreground hover:text-accent-foreground",
                            isCollapsed && !mobile ? "justify-center px-2" : ""
                        )}
                    >
                        <item.icon className={cn("h-4 w-4", isCollapsed && !mobile ? "" : "mr-2")} />
                        {(!isCollapsed || mobile) && <span>{item.label}</span>}
                    </NavLink>
                </TooltipTrigger>
                {isCollapsed && !mobile && (
                    <TooltipContent side="right">
                        <p>{item.label}</p>
                    </TooltipContent>
                )}
            </Tooltip>
        </TooltipProvider>
    );

    return (
        <div className="flex h-screen bg-background overflow-hidden">
            {/* Mobile Navigation */}
            <Sheet open={isMobileOpen} onOpenChange={setMobileOpen}>
                <SheetTrigger asChild>
                    <Button
                        variant="outline"
                        size="icon"
                        className="xl:hidden fixed top-4 left-4 z-50"
                    >
                        <MenuIcon className="h-4 w-4" />
                        <span className="sr-only">Toggle navigation menu</span>
                    </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-64 p-0">
                    <div className="flex h-full flex-col">
                        <div className="flex h-14 items-center border-b px-4">
                            <h1 className="text-lg font-semibold">GeminiBlog</h1>
                            <Badge variant="secondary" className="ml-2">Admin</Badge>
                        </div>
                        <nav className="flex-1 space-y-1 p-4">
                            {navItems.map((item) => (
                                <NavItem key={item.to} item={item} mobile />
                            ))}
                        </nav>
                        <div className="border-t p-4">
                            <Button
                                variant="ghost"
                                className="w-full justify-start"
                                onClick={handleLogout}
                            >
                                <ArrowLeftOnRectangleIcon className="mr-2 h-4 w-4" />
                                Logout
                            </Button>
                        </div>
                    </div>
                </SheetContent>
            </Sheet>

            {/* Desktop Sidebar */}
            <aside className={cn(
                "hidden xl:flex flex-col border-r bg-card transition-all duration-300",
                isCollapsed ? "w-16" : "w-64"
            )}>
                {/* Desktop Collapse Toggle */}
                <div className="flex h-14 items-center justify-between border-b px-4">
                    {!isCollapsed && (
                        <div className="flex items-center gap-2">
                            <h1 className="text-lg font-semibold">GeminiBlog</h1>
                            <Badge variant="secondary">Admin</Badge>
                        </div>
                    )}
                    {isCollapsed && (
                        <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto">
                            <span className="text-primary-foreground font-bold text-sm">GB</span>
                        </div>
                    )}
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleCollapsed}
                        className="h-8 w-8"
                    >
                        {isCollapsed ? (
                            <ChevronRightIcon className="h-4 w-4" />
                        ) : (
                            <ChevronLeftIcon className="h-4 w-4" />
                        )}
                        <span className="sr-only">
                            {isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                        </span>
                    </Button>
                </div>

                {/* Desktop Navigation */}
                <nav className="flex-1 space-y-1 p-4">
                    {navItems.map((item) => (
                        <NavItem key={item.to} item={item} />
                    ))}
                </nav>

                {/* Desktop Logout */}
                <div className="border-t p-4">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="ghost"
                                    className={cn(
                                        "w-full",
                                        isCollapsed ? "justify-center px-2" : "justify-start"
                                    )}
                                    onClick={handleLogout}
                                >
                                    <ArrowLeftOnRectangleIcon className="h-4 w-4" />
                                    {!isCollapsed && <span className="ml-2">Logout</span>}
                                </Button>
                            </TooltipTrigger>
                            {isCollapsed && (
                                <TooltipContent side="right">
                                    <p>Logout</p>
                                </TooltipContent>
                            )}
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </aside>

            {/* Main Content */}
            <main className="flex-1 flex flex-col overflow-hidden">
                {/* Content Area - Fixed height, no scrolling */}
                <div className="flex-1 overflow-hidden p-4 sm:p-6">
                    <div className="h-full">
                        <Outlet />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default AdminLayout;
