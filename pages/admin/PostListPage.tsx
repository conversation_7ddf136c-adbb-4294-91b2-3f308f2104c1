
import React, { useContext } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { BlogContext } from '../../context/SupabaseBlogContext';
import { PlusIcon, PencilIcon, TrashIcon, EyeIcon } from '../../components/icons';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const PostListPage: React.FC = () => {
    const context = useContext(BlogContext);
    const navigate = useNavigate();

    if (!context) {
        return (
            <div className="flex items-center justify-center h-full">
                <div className="text-muted-foreground">Loading...</div>
            </div>
        );
    }

    const { posts, deletePost, categories } = context;

    const getCategoryName = (categoryId: string) => {
        return categories.find(c => c.id === categoryId)?.name || 'Uncategorized';
    };

    const handleDeletePost = async (postId: string, postTitle: string) => {
        if (window.confirm(`Are you sure you want to delete "${postTitle}"? This action cannot be undone.`)) {
            try {
                await deletePost(postId);
            } catch (error) {
                console.error('Failed to delete post:', error);
            }
        }
    };

    return (
        <div className="h-full flex flex-col space-y-6">
            {/* Header */}
            <div className="flex-shrink-0 flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Manage Posts</h1>
                    <p className="text-muted-foreground">
                        Create, edit, and manage your blog posts
                    </p>
                </div>
                <Button onClick={() => navigate('/admin/posts/new')}>
                    <PlusIcon className="w-4 h-4 mr-2" />
                    New Post
                </Button>
            </div>
            {/* Posts Table */}
            <div className="flex-1 min-h-0">
                <Card className="h-full">
                    <CardHeader>
                        <CardTitle>All Posts ({posts.length})</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-auto h-full">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Title</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {posts.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center py-8">
                                                <div className="text-muted-foreground">
                                                    No posts found. Create your first post to get started!
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        posts.map(post => (
                                            <TableRow key={post.id}>
                                                <TableCell className="font-medium">
                                                    <div className="max-w-[300px] truncate">
                                                        {post.title}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {getCategoryName(post.categoryId)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        variant={post.status === 'published' ? 'default' : 'secondary'}
                                                    >
                                                        {post.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {new Date(post.createdAt).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <TooltipProvider>
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        asChild
                                                                    >
                                                                        <Link to={`/post/${post.slug}`} target="_blank">
                                                                            <EyeIcon className="h-4 w-4" />
                                                                        </Link>
                                                                    </Button>
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <p>View Post</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>

                                                        <TooltipProvider>
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        onClick={() => navigate(`/admin/posts/edit/${post.id}`)}
                                                                    >
                                                                        <PencilIcon className="h-4 w-4" />
                                                                    </Button>
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <p>Edit Post</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>

                                                        <TooltipProvider>
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        onClick={() => handleDeletePost(post.id, post.title)}
                                                                        className="text-destructive hover:text-destructive"
                                                                    >
                                                                        <TrashIcon className="h-4 w-4" />
                                                                    </Button>
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <p>Delete Post</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default PostListPage;
