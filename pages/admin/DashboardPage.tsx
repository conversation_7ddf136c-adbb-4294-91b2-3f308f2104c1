
import React, { useContext } from 'react';
import { BlogContext } from '../../context/SupabaseBlogContext';
import AnalyticsChart from '../../components/AnalyticsChart';
import { DocumentTextIcon, TagIcon } from '../../components/icons';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

const DashboardPage: React.FC = () => {
    const context = useContext(BlogContext);

    if (!context) {
        return (
            <div className="flex items-center justify-center h-full">
                <div className="text-muted-foreground">Loading...</div>
            </div>
        );
    }

    const { posts, categories, tags } = context;
    const publishedCount = posts.filter(p => p.status === 'published').length;
    const draftCount = posts.filter(p => p.status === 'draft').length;

    const StatCard = ({ title, value, icon }: { title: string, value: number, icon: React.ReactNode }) => (
        <Card>
            <CardContent className="flex items-center p-6">
                <div className="p-3 bg-primary/10 rounded-full mr-4 text-primary">
                    {icon}
                </div>
                <div>
                    <p className="text-sm text-muted-foreground">{title}</p>
                    <p className="text-2xl font-bold">{value}</p>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <div className="h-full flex flex-col space-y-6">
            {/* Header */}
            <div className="flex-shrink-0">
                <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
                <p className="text-muted-foreground">
                    Welcome to your blog administration panel
                </p>
            </div>

            {/* Stats Cards */}
            <div className="flex-shrink-0 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard title="Total Posts" value={posts.length} icon={<DocumentTextIcon className="w-6 h-6" />} />
                <StatCard title="Published" value={publishedCount} icon={<DocumentTextIcon className="w-6 h-6" />} />
                <StatCard title="Drafts" value={draftCount} icon={<DocumentTextIcon className="w-6 h-6" />} />
                <StatCard title="Categories" value={categories.length} icon={<TagIcon className="w-6 h-6" />} />
            </div>

            {/* Main Content Grid */}
            <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 min-h-0">
                {/* Analytics Chart */}
                <div className="lg:col-span-2">
                    <Card className="h-full">
                        <CardHeader>
                            <CardTitle>Analytics Overview</CardTitle>
                        </CardHeader>
                        <CardContent className="h-full">
                            <AnalyticsChart />
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Posts */}
                <Card className="h-full">
                    <CardHeader>
                        <CardTitle>Recent Posts</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <ScrollArea className="h-[400px]">
                            <div className="p-6 space-y-4">
                                {posts.slice(0, 10).map((post, index) => (
                                    <div key={post.id}>
                                        <div className="flex justify-between items-start">
                                            <div className="space-y-1 flex-1">
                                                <p className="font-medium leading-none line-clamp-2">
                                                    {post.title}
                                                </p>
                                                <div className="flex items-center gap-2">
                                                    <Badge
                                                        variant={post.status === 'published' ? 'default' : 'secondary'}
                                                        className="text-xs"
                                                    >
                                                        {post.status}
                                                    </Badge>
                                                    <span className="text-xs text-muted-foreground">
                                                        {new Date(post.createdAt).toLocaleDateString()}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        {index < posts.slice(0, 10).length - 1 && (
                                            <Separator className="mt-4" />
                                        )}
                                    </div>
                                ))}
                                {posts.length === 0 && (
                                    <div className="text-center text-muted-foreground py-8">
                                        No posts yet. Create your first post to get started!
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default DashboardPage;
