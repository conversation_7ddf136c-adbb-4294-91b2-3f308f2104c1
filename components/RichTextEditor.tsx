import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { BlogContext } from '../context/SupabaseBlogContext';
import { useDebounce } from '../hooks/useDebounce';
import './RichTextEditor.css';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number | string;
  disabled?: boolean;
  className?: string;
  autoHeight?: boolean;
  // Enhanced features
  enableAutoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (content: string) => Promise<void>;
  showWordCount?: boolean;
  showDetailedStats?: boolean;
  enableKeyboardShortcuts?: boolean;
  enableMediaUpload?: boolean;
  enableLinking?: boolean;
  enableTables?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = React.memo(({
  value,
  onChange,
  placeholder = 'Start writing your content...',
  height = 400,
  disabled = false,
  className = '',
  autoHeight = false,
  enableAutoSave = true,
  autoSaveDelay = 30000,
  onAutoSave,
  showWordCount = true,
  showDetailedStats = false,
  enableKeyboardShortcuts = true,
  enableMediaUpload = true,
  enableLinking = true,
  enableTables = true
}) => {
  const context = useContext(BlogContext);
  const uploadPostImage = context?.uploadPostImage;
  const editorRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);

  // Get theme-aware colors
  const getThemeColors = useCallback(() => {
    const isDark = document.documentElement.classList.contains('dark');

    if (isDark) {
      return {
        background: '#1f2937', // dark card
        foreground: '#f9fafb', // dark foreground
        muted: '#374151', // dark muted
        mutedForeground: '#9ca3af', // dark muted-foreground
        border: '#4b5563', // dark border
        primary: '#3b82f6' // primary blue
      };
    } else {
      return {
        background: '#ffffff', // light card
        foreground: '#1f2937', // light foreground
        muted: '#f9fafb', // light muted
        mutedForeground: '#6b7280', // light muted-foreground
        border: '#e5e7eb', // light border
        primary: '#3b82f6' // primary blue
      };
    }
  }, []);

  // Debounced onChange to prevent excessive parent updates
  const debouncedOnChange = useDebounce(onChange, 300);

  // Auto-save functionality
  const debouncedAutoSave = useDebounce(async (content: string) => {
    if (onAutoSave && enableAutoSave && content.trim()) {
      try {
        await onAutoSave(content);
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }
  }, autoSaveDelay);

  // Calculate reading time (average 200 words per minute)
  const calculateReadingTime = useCallback((text: string) => {
    const words = text.trim().split(/\s+/).length;
    return Math.ceil(words / 200);
  }, []);

  // Update statistics
  const updateStats = useCallback((content: string) => {
    const textContent = content.replace(/<[^>]*>/g, ''); // Strip HTML tags
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    const characters = textContent.length;
    const reading = calculateReadingTime(textContent);

    setWordCount(words);
    setCharacterCount(characters);
    setReadingTime(reading);
  }, [calculateReadingTime]);

  // Handle content change
  const handleEditorChange = useCallback((content: string) => {
    updateStats(content);
    debouncedOnChange(content);
    debouncedAutoSave(content);
  }, [updateStats, debouncedOnChange, debouncedAutoSave]);

  // Handle image upload
  const handleImageUpload = useCallback(async (blobInfo: any, progress: (percent: number) => void): Promise<string> => {
    if (!enableMediaUpload || !uploadPostImage) {
      throw new Error('Image upload is not enabled');
    }

    try {
      progress(0);
      
      // Convert blob to File
      const file = new File([blobInfo.blob()], blobInfo.filename(), {
        type: blobInfo.blob().type,
      });

      progress(50);
      
      const result = await uploadPostImage(file);
      progress(100);

      return result.medium; // Use medium size for editor
    } catch (error) {
      console.error('Image upload failed:', error);
      throw error;
    }
  }, [enableMediaUpload, uploadPostImage]);

  // TinyMCE configuration
  const editorConfig = useMemo(() => ({
    apiKey: (import.meta as any).env?.VITE_TINYMCE_API_KEY,
    height: autoHeight ? 'auto' : (typeof height === 'number' ? Math.max(height, 400) : height),
    min_height: 400,
    max_height: 800,
    menubar: false,
    statusbar: true,

    // WordPress-like format options
    block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre',
    font_size_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt',
    font_family_formats: 'Arial=arial,helvetica,sans-serif; Georgia=georgia,serif; Helvetica=helvetica,arial,sans-serif; Times New Roman=times new roman,times,serif; Verdana=verdana,geneva,sans-serif; Courier New=courier new,courier,monospace',

    // Performance and Privacy Optimizations
    referrer_policy: 'origin' as ReferrerPolicy,
    convert_urls: false,
    relative_urls: false,
    remove_script_host: false,
    document_base_url: window.location.origin,

    // Disable analytics and telemetry to prevent network blocking errors
    analytics: {
      enabled: false
    },

    // Performance optimizations
    browser_spellcheck: true,
    contextmenu_never_use_native: true,

    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
      'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
      'autosave', 'save', 'directionality', 'nonbreaking', 'pagebreak',
      'quickbars', 'codesample', 'accordion'
    ].filter(plugin => {
      // Conditionally include plugins based on props
      if (!enableTables && plugin === 'table') return false;
      if (!enableMediaUpload && (plugin === 'image' || plugin === 'media')) return false;
      if (!enableLinking && plugin === 'link') return false;
      if (!showWordCount && plugin === 'wordcount') return false;
      if (!enableAutoSave && plugin === 'autosave') return false;
      return true;
    }),
    toolbar: [
      'undo redo | formatselect | bold italic underline strikethrough',
      'forecolor backcolor | alignleft aligncenter alignright alignjustify',
      'bullist numlist outdent indent | blockquote hr',
      enableLinking ? 'link unlink' : '',
      enableMediaUpload ? 'image media' : '',
      enableTables ? 'table' : '',
      'codesample | removeformat | help'
    ].filter(Boolean).join(' | '),

    // WordPress-like toolbar styling
    toolbar_mode: 'sliding' as any,
    toolbar_sticky: true,
    toolbar_sticky_offset: 0,
    quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
    quickbars_insert_toolbar: 'quickimage quicktable',
    contextmenu: 'link image table',
    skin: 'oxide',
    content_css: 'default',
    content_style: (() => {
      const colors = getThemeColors();
      return `
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          font-size: 16px;
          line-height: 1.6;
          color: ${colors.foreground} !important;
          margin: 16px;
          padding: 16px;
          background: ${colors.background} !important;
          min-height: 400px;
          border: none;
          outline: none;
        }

        .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
          color: ${colors.mutedForeground} !important;
          font-style: italic;
          content: attr(data-mce-placeholder);
          position: absolute;
          top: 16px;
          left: 16px;
          pointer-events: none;
        }

        /* WordPress-like styling */
        h1, h2, h3, h4, h5, h6 {
          color: ${colors.foreground} !important;
          font-weight: 600;
          margin: 1em 0 0.5em 0;
          line-height: 1.3;
        }

        h1 { font-size: 2.25em; }
        h2 { font-size: 1.875em; }
        h3 { font-size: 1.5em; }
        h4 { font-size: 1.25em; }
        h5 { font-size: 1.125em; }
        h6 { font-size: 1em; }

        p {
          margin: 0 0 1em 0;
          line-height: 1.6;
          color: ${colors.foreground} !important;
        }

        blockquote {
          border-left: 4px solid ${colors.border};
          margin: 1.5em 0;
          padding: 0.5em 0 0.5em 1em;
          font-style: italic;
          color: ${colors.mutedForeground} !important;
        }

        ul, ol {
          margin: 1em 0;
          padding-left: 2em;
        }

        li {
          margin: 0.25em 0;
          color: ${colors.foreground} !important;
        }

        table {
          border-collapse: collapse;
          width: 100%;
          margin: 1em 0;
        }

        table td, table th {
          border: 1px solid ${colors.border};
          padding: 8px 12px;
          text-align: left;
          color: ${colors.foreground} !important;
        }

        table th {
          background-color: ${colors.muted};
          font-weight: 600;
          color: ${colors.foreground} !important;
        }

        code {
          background-color: ${colors.muted};
          color: ${colors.foreground} !important;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.875em;
        }

        pre {
          background-color: ${colors.muted};
          color: ${colors.foreground} !important;
          padding: 1em;
          border-radius: 6px;
          overflow-x: auto;
          margin: 1em 0;
        }

        img {
          max-width: 100%;
          height: auto;
          border-radius: 6px;
          margin: 0.5em 0;
        }

        a {
          color: ${colors.primary} !important;
          text-decoration: underline;
        }

        a:hover {
          color: ${colors.primary} !important;
          opacity: 0.8;
        }
      `;
    })(),
    placeholder,
    branding: false,
    promotion: false,
    resize: !autoHeight,

    // Additional privacy and performance settings
    allow_script_urls: false,
    allow_unsafe_link_target: false,
    convert_fonts_to_spans: true,
    fix_list_elements: true,
    forced_root_block: 'p',
    forced_root_block_attrs: {
      style: 'margin: 0; padding: 0;'
    },

    // Prevent external requests that might be blocked
    external_plugins: {},

    // Content filtering for security and performance
    valid_elements: '*[*]',
    extended_valid_elements: 'script[src|async|defer|type|charset]',

    // Performance: Reduce DOM queries
    cache_suffix: '?v=7.0.0',
    autosave_ask_before_unload: enableAutoSave,
    autosave_interval: enableAutoSave ? `${autoSaveDelay / 1000}s` : undefined,
    autosave_prefix: 'tinymce-autosave-{path}{query}-{id}-',
    autosave_restore_when_empty: enableAutoSave,
    images_upload_handler: enableMediaUpload ? handleImageUpload : undefined,
    automatic_uploads: enableMediaUpload,
    file_picker_types: enableMediaUpload ? 'image' : undefined,
    paste_data_images: enableMediaUpload,
    image_advtab: true,
    image_caption: true,
    image_title: true,
    link_assume_external_targets: true,
    link_context_toolbar: true,

    // Updated table configuration (TinyMCE 7.0 compatible)
    table_use_colgroups: true,
    table_sizing_mode: 'responsive',
    table_default_attributes: {
      class: 'table table-striped',
      style: 'width: 100%; border-collapse: collapse;'
    },
    table_default_styles: {
      width: '100%',
      'border-collapse': 'collapse'
    },
    table_cell_advtab: true,
    table_row_advtab: true,
    table_advtab: true,
    codesample_languages: [
      { text: 'HTML/XML', value: 'markup' },
      { text: 'JavaScript', value: 'javascript' },
      { text: 'TypeScript', value: 'typescript' },
      { text: 'CSS', value: 'css' },
      { text: 'Python', value: 'python' },
      { text: 'Java', value: 'java' },
      { text: 'C#', value: 'csharp' },
      { text: 'PHP', value: 'php' },
      { text: 'Ruby', value: 'ruby' },
      { text: 'Go', value: 'go' },
      { text: 'Rust', value: 'rust' },
      { text: 'SQL', value: 'sql' },
      { text: 'JSON', value: 'json' },
      { text: 'Bash', value: 'bash' }
    ],
    setup: (editor: any) => {
      // Performance optimization: Batch DOM updates
      editor.on('init', () => {
        setIsInitialized(true);
        setIsLoading(false);

        // Apply theme-aware styling to editor body
        const body = editor.getBody();
        if (body) {
          const colors = getThemeColors();
          body.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif';
          body.style.fontSize = '16px';
          body.style.lineHeight = '1.6';
          body.style.color = colors.foreground;
          body.style.backgroundColor = colors.background;
          body.style.padding = '16px';
          body.style.minHeight = '350px';
        }
      });

      // Error handling for network issues
      editor.on('LoadError', (e: any) => {
        console.warn('TinyMCE load error (likely network/privacy blocker):', e);
        // Continue without failing - editor should still work
      });

      // Custom keyboard shortcuts
      if (enableKeyboardShortcuts) {
        editor.addShortcut('Meta+S', 'Save content', () => {
          if (onAutoSave) {
            onAutoSave(editor.getContent());
          }
        });

        // Additional shortcuts for better UX
        editor.addShortcut('Meta+B', 'Bold', () => {
          editor.execCommand('Bold');
        });

        editor.addShortcut('Meta+I', 'Italic', () => {
          editor.execCommand('Italic');
        });
      }

      // Performance: Debounce content changes
      let contentChangeTimeout: NodeJS.Timeout;
      editor.on('input', () => {
        clearTimeout(contentChangeTimeout);
        contentChangeTimeout = setTimeout(() => {
          // Trigger any necessary updates
        }, 100);
      });
    }
  }), [
    height, autoHeight, placeholder, enableTables, enableMediaUpload, enableLinking,
    showWordCount, enableAutoSave, autoSaveDelay, handleImageUpload, onAutoSave,
    enableKeyboardShortcuts, getThemeColors
  ]);

  // Initialize statistics on mount
  useEffect(() => {
    if (value) {
      updateStats(value);
    }
  }, [value, updateStats]);

  // Listen for theme changes and update editor styling
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          // Theme changed, update editor styling
          if (editorRef.current && isInitialized) {
            const editor = editorRef.current;
            const body = editor.getBody();
            if (body) {
              const colors = getThemeColors();
              body.style.color = colors.foreground;
              body.style.backgroundColor = colors.background;
            }
          }
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, [isInitialized, getThemeColors]);

  return (
    <div className={`tinymce-editor-container ${className}`}>
      <div className="editor-wrapper">
        {isLoading && (
          <div className="tinymce-loading">
            Loading editor...
          </div>
        )}
        <Editor
          ref={editorRef}
          apiKey={(import.meta as any).env?.VITE_TINYMCE_API_KEY}
          value={value}
          onEditorChange={handleEditorChange}
          disabled={disabled}
          init={editorConfig}
          onLoadContent={() => {
            // Performance: Initialize stats after content loads
            if (value) {
              updateStats(value);
            }
          }}
          onInit={() => {
            // Additional initialization optimizations
            setIsInitialized(true);
            setIsLoading(false);
          }}
        />
      </div>

      {/* Statistics Display */}
      {(showWordCount || showDetailedStats) && isInitialized && (
        <div className="editor-stats">
          {showWordCount && (
            <>
              <span>📝 {wordCount} words</span>
              <span>🔤 {characterCount} characters</span>
              <span>⏱️ {readingTime} min read</span>
            </>
          )}
          {showDetailedStats && (
            <span>✅ Editor ready</span>
          )}
        </div>
      )}
    </div>
  );
});

// Add display name for debugging
RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
