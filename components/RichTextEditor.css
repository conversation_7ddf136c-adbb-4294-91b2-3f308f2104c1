/* TinyMCE Editor Styling */
.tinymce-editor-container {
  width: 100%;
  position: relative;
}

.editor-wrapper {
  background: hsl(var(--card));
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid hsl(var(--border));
}

/* TinyMCE Toolbar Styling */
.tox .tox-toolbar {
  background: hsl(var(--muted)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
  padding: 8px 12px !important;
}

.tox .tox-toolbar__group {
  border: none !important;
}

.tox .tox-tbtn {
  border-radius: 4px !important;
  margin: 0 2px !important;
  color: hsl(var(--foreground)) !important;
}

.tox .tox-tbtn:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.tox .tox-tbtn--enabled {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.tox .tox-tbtn--enabled:hover {
  background: hsl(var(--primary)) !important;
  opacity: 0.9;
}

/* Editor Content Area */
.tox .tox-edit-area {
  border: none !important;
}

.tox .tox-edit-area__iframe {
  background: hsl(var(--card)) !important;
}

/* Status Bar */
.tox .tox-statusbar {
  background: hsl(var(--muted)) !important;
  border-top: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--muted-foreground)) !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
}

/* Dropdown Menus */
.tox .tox-collection__item {
  color: hsl(var(--foreground)) !important;
}

.tox .tox-collection__item:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.tox .tox-collection__item--active {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* Dialog Styling */
.tox .tox-dialog {
  border-radius: 8px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.tox .tox-dialog__header {
  background: hsl(var(--muted)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.tox .tox-dialog__title {
  color: hsl(var(--foreground)) !important;
  font-weight: 600 !important;
}

.tox .tox-button {
  border-radius: 6px !important;
  font-weight: 500 !important;
  background: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.tox .tox-button--primary {
  background: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.tox .tox-button--primary:hover {
  background: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
  opacity: 0.9;
}

/* Loading State */
.tox .tox-throbber {
  background: hsl(var(--background) / 0.8) !important;
}

/* Focus States */
.tox.tox-tinymce:focus-within {
  box-shadow: 0 0 0 2px hsl(var(--ring)) !important;
  border-color: hsl(var(--ring)) !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .tox .tox-toolbar {
    padding: 6px 8px !important;
  }
  
  .tox .tox-tbtn {
    margin: 0 1px !important;
    padding: 6px !important;
  }
  
  .tox .tox-toolbar__group {
    margin: 0 4px 0 0 !important;
  }
}

/* Dark Mode Support - Using .dark class instead of media query */
.dark .tinymce-editor-container .editor-wrapper {
  background: hsl(var(--card));
  border-color: hsl(var(--border));
}

.dark .tox .tox-toolbar {
  background: hsl(var(--muted)) !important;
  border-bottom-color: hsl(var(--border)) !important;
}

.dark .tox .tox-tbtn {
  color: hsl(var(--foreground)) !important;
}

.dark .tox .tox-tbtn:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dark .tox .tox-statusbar {
  background: hsl(var(--muted)) !important;
  border-top-color: hsl(var(--border)) !important;
  color: hsl(var(--muted-foreground)) !important;
}

.dark .tox .tox-edit-area__iframe {
  background: hsl(var(--card)) !important;
}

/* Custom Animations */
.tox .tox-toolbar,
.tox .tox-statusbar {
  transition: all 0.2s ease-in-out;
}

.tox .tox-tbtn {
  transition: all 0.15s ease-in-out;
}

/* WordPress-like Styling */
.tinymce-editor-container .editor-wrapper {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Statistics Display */
.editor-stats {
  background: hsl(var(--muted));
  border-top: 1px solid hsl(var(--border));
  padding: 8px 12px;
  font-size: 12px;
  color: hsl(var(--muted-foreground));
  display: flex;
  gap: 16px;
  align-items: center;
}

.editor-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Loading Indicator */
.tinymce-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  font-size: 14px;
}

.tinymce-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid hsl(var(--border));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
